{"Version": 3, "Meta": {"PhysicsSettingCount": 19, "TotalInputCount": 33, "TotalOutputCount": 45, "VertexCount": 88, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "头X"}, {"Id": "PhysicsSetting2", "Name": "头Y"}, {"Id": "PhysicsSetting3", "Name": "头Z"}, {"Id": "PhysicsSetting4", "Name": "身体X"}, {"Id": "PhysicsSetting5", "Name": "身体Y"}, {"Id": "PhysicsSetting6", "Name": "身体Z"}, {"Id": "PhysicsSetting7", "Name": "身体XX"}, {"Id": "PhysicsSetting8", "Name": "身体YY"}, {"Id": "PhysicsSetting9", "Name": "身体ZZ"}, {"Id": "PhysicsSetting10", "Name": "裙XZ"}, {"Id": "PhysicsSetting11", "Name": "发XZ"}, {"Id": "PhysicsSetting12", "Name": "胸XZ"}, {"Id": "PhysicsSetting13", "Name": "裙Y"}, {"Id": "PhysicsSetting14", "Name": "发Y"}, {"Id": "PhysicsSetting15", "Name": "胸Y"}, {"Id": "PhysicsSetting16", "Name": "手XYZ"}, {"Id": "PhysicsSetting17", "Name": "手XYZ(2)"}, {"Id": "PhysicsSetting18", "Name": "右眼"}, {"Id": "PhysicsSetting19", "Name": "左眼"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 7, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamAngleX2"}, "VertexIndex": 1, "Scale": 172.122, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.9, "Acceleration": 1.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamAngleY2"}, "VertexIndex": 1, "Scale": 172.122, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.9, "Acceleration": 1.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 7, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "VertexIndex": 1, "Scale": 172.122, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.9, "Acceleration": 1.7, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 7, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "VertexIndex": 1, "Scale": 57.296, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "VertexIndex": 1, "Scale": 57.296, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "VertexIndex": 1, "Scale": 57.296, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX2"}, "VertexIndex": 1, "Scale": 57.296, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 1.1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY2"}, "VertexIndex": 1, "Scale": 57.296, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 1.1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 7, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "VertexIndex": 1, "Scale": 57.296, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.6, "Delay": 0.6, "Acceleration": 1.1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 25, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 2, "Scale": 37, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 3, "Scale": 36, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param3"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.3, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 1, "Scale": 38, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param9"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param10"}, "VertexIndex": 3, "Scale": 38, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param65"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param66"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 1, "Scale": 38, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param15"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 1, "Scale": 32, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param5"}, "VertexIndex": 2, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 1, "Scale": 32, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 2, "Scale": 34, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param14"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param67"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.5, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 1, "Scale": 32, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 2, "Scale": 34, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 1.3, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param31"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param34"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 0.9, "Radius": 12}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.88, "Delay": 0.9, "Acceleration": 0.9, "Radius": 12}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param32"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param33"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param58"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param35"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param36"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param59"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param60"}, "VertexIndex": 4, "Scale": 55, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param61"}, "VertexIndex": 4, "Scale": 55, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.88, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.88, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.88, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.88, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 50}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param44"}, "VertexIndex": 1, "Scale": 23, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param45"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param49"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -30, "Default": 0, "Maximum": 0}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 0}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param46"}, "VertexIndex": 1, "Scale": 23, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param47"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param48"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -30, "Default": 0, "Maximum": 0}, "Angle": {"Minimum": -50, "Default": 0, "Maximum": 0}}}]}