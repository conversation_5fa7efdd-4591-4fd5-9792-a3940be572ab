# Live2D 模型文件说明

本目录包含了一个完整的 Live2D Cubism 3.0 模型文件集合，用于在 Web 应用中展示 2D 虚拟角色。

## 📁 目录结构概览

```
public/live2d/
├── 📄 核心模型文件
├── 🎭 表情文件 (.exp3.json)
├── 🎬 动作文件 (.motion3.json)
├── 🖼️ 纹理资源文件夹
├── ⚙️ 配置文件
└── 📋 说明文件
```

## 🔧 核心模型文件

### 主配置文件
- **`LIVE 2D 384.model3.json`** - 主模型配置文件
  - 定义模型的基本结构和文件引用关系
  - 包含纹理、物理、显示信息等文件路径
  - 定义参数组（如眨眼、口型同步）

### 模型数据文件
- **`LIVE 2D 384.moc3`** - 模型数据文件（二进制）
  - 包含模型的网格、变形器、参数等核心数据
  - 由 Live2D Cubism Editor 导出生成
  - 文件大小较大，包含完整的模型结构信息

### 物理配置文件
- **`LIVE 2D 384.physics3.json`** - 物理模拟配置
  - 定义头发、衣物等部件的物理运动规律
  - 包含重力、阻尼、弹性等物理参数
  - 使模型动作更加自然流畅

### 显示信息文件
- **`LIVE 2D 384.cdi3.json`** - 显示信息配置
  - 定义模型的显示属性和渲染参数
  - 包含混合模式、透明度等渲染设置

### VTube 配置文件
- **`LIVE 2D 384.vtube.json`** - VTuber 应用配置
  - 用于 VTuber 直播应用的特殊配置
  - 包含面部追踪和表情映射设置

## 🖼️ 纹理资源

### 纹理文件夹
- **`LIVE 2D 384.4096/`** - 纹理资源目录
  - **`texture_00.png`** - 主纹理贴图（4096x4096）
  - **`texture_01.png`** - 辅助纹理贴图（4096x4096）
  - 高分辨率纹理确保模型显示清晰度

## 🎭 表情文件 (.exp3.json)

表情文件定义了角色的各种面部表情，通过调整特定参数值实现：

### 情感表情
- **`傲娇.exp3.json`** - 傲娇表情
- **`害羞.exp3.json`** - 害羞表情
- **`委屈.exp3.json`** - 委屈表情
- **`生气.exp3.json`** - 生气表情
- **`惊喜.exp3.json`** - 惊喜表情
- **`惊讶.exp3.json`** - 惊讶表情
- **`温柔的笑.exp3.json`** - 温柔微笑表情

### 动作表情
- **`叉腰.exp3.json`** - 叉腰姿势表情
- **`抱胸.exp3.json`** - 抱胸姿势表情
- **`托腮.exp3.json`** - 托腮姿势表情
- **`挥手.exp3.json`** - 挥手表情

### 特殊表情
- **`哈哈大笑.exp3.json`** - 大笑表情
- **`眯眯眼.exp3.json`** - 眯眼表情
- **`眼泪.exp3.json`** - 流泪表情
- **`落泪.exp3.json`** - 落泪表情
- **`脸红.exp3.json`** - 脸红表情
- **`鬼脸.exp3.json`** - 鬼脸表情

### 工作表情
- **`电脑.exp3.json`** - 使用电脑表情
- **`电脑发光.exp3.json`** - 电脑发光效果表情
- **`键盘抬起.exp3.json`** - 键盘操作表情

## 🎬 动作文件 (.motion3.json)

动作文件定义了角色的各种动态行为，包含时间轴和参数变化：

### 基础动作
- **`基础动画.motion3.json`** - 基础待机动画
  - 循环播放的基础动作
  - 包含自然的呼吸和微动效果
  - 持续时间：7秒，60FPS

### 交互动作
- **`挥手.motion3.json`** - 挥手动作
- **`点头.motion3.json`** - 点头动作
- **`摇头.motion3.json`** - 摇头动作
- **`眼珠子.motion3.json`** - 眼球转动动作

### 状态动作
- **`睡觉.motion3.json`** - 睡觉动作
- **`sleep.motion3.json`** - 睡眠状态动作

## ⚙️ 配置文件

### 模型绑定配置
- **`items_pinned_to_model.json`** - 模型绑定物品配置
  - 定义附加到模型上的装饰品或道具
  - 包含位置、旋转、缩放等变换信息

## 📋 文件格式说明

### .model3.json 格式
```json
{
  "Version": 3,
  "FileReferences": {
    "Moc": "模型数据文件路径",
    "Textures": ["纹理文件路径数组"],
    "Physics": "物理配置文件路径",
    "DisplayInfo": "显示信息文件路径"
  },
  "Groups": [
    {
      "Target": "Parameter",
      "Name": "参数组名称",
      "Ids": ["参数ID数组"]
    }
  ]
}
```

### .exp3.json 格式
```json
{
  "Type": "Live2D Expression",
  "Parameters": [
    {
      "Id": "参数ID",
      "Value": "参数值",
      "Blend": "混合模式"
    }
  ]
}
```

### .motion3.json 格式
```json
{
  "Version": 3,
  "Meta": {
    "Duration": "动画时长",
    "Fps": "帧率",
    "Loop": "是否循环",
    "CurveCount": "曲线数量"
  },
  "Curves": [
    {
      "Target": "目标类型",
      "Id": "参数ID",
      "Segments": ["时间轴数据"]
    }
  ]
}
```

## 🔧 技术规格

- **Live2D 版本**: Cubism 3.0
- **纹理分辨率**: 4096x4096
- **动画帧率**: 60 FPS
- **文件编码**: UTF-8
- **支持平台**: Web (WebGL)

## 📝 使用说明

1. **模型加载**: 通过 `LIVE 2D 384.model3.json` 加载完整模型
2. **表情切换**: 使用 `.exp3.json` 文件切换角色表情
3. **动作播放**: 使用 `.motion3.json` 文件播放角色动作
4. **物理效果**: 自动应用 `physics3.json` 中定义的物理效果

## ⚠️ 注意事项

- 所有文件路径区分大小写
- 纹理文件较大，建议进行适当的压缩优化
- 动作文件包含复杂的时间轴数据，修改时需谨慎
- 建议在修改前备份原始文件

## 🔗 相关资源

- [Live2D Cubism 官方文档](https://docs.live2d.com/)
- [Live2D Web SDK](https://github.com/guansss/pixi-live2d-display)
- [Live2D 模型制作教程](https://www.live2d.com/learn/tutorial/)

---

*此文档描述了 Live2D 模型的完整文件结构，用于开发和维护参考。*
